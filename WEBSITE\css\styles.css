/* CABLYS Website Styles */

:root {
  --primary-color: #1976d2;
  --primary-dark: #0d47a1;
  --primary-light: #42a5f5;
  --secondary-color: #dc004e;
  --secondary-dark: #9a0036;
  --secondary-light: #ff5c8d;
  --success-color: #2e7d32;
  --warning-color: #ed6c02;
  --error-color: #d32f2f;
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --text-primary: #212121;
  --text-secondary: #757575;
  --border-color: #e0e0e0;
}

/* General styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
}

/* Button styles */
.btn-primary {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.3s ease, transform 0.2s ease;
  border: none;
  cursor: pointer;
  text-align: center;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  color: white;
}

.btn-login {
  display: inline-block;
  background-color: transparent;
  color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  border: 2px solid var(--primary-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.btn-login:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Header styles */
header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 15px 0;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.logo-container h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  letter-spacing: 0.5px;
}

nav ul {
  display: flex;
  list-style: none;
}

nav ul li {
  margin-left: 25px;
}

nav ul li a {
  color: var(--text-primary);
  font-weight: 500;
  position: relative;
}

nav ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

nav ul li a:hover::after {
  width: 100%;
}

/* Hero section */
#hero {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  color: white;
  padding: 100px 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

/* Section styles */
section {
  padding: 80px 0;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 50px;
  color: var(--text-primary);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
}

/* Services section */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.service-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.service-icon {
  margin-bottom: 20px;
}

.service-icon img {
  width: 60px;
  height: 60px;
}

.service-card h3 {
  margin-bottom: 15px;
  color: var(--primary-color);
}

/* About section */
.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

.about-text p {
  margin-bottom: 20px;
}

.about-image img {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Contact section */
.contact-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
}

.contact-info {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.contact-info h3 {
  margin-bottom: 20px;
  color: var(--primary-color);
}

.contact-info p {
  margin-bottom: 10px;
}

.contact-form {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
  margin-bottom: 20px;
  color: var(--primary-color);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: 'Roboto', sans-serif;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* Footer styles */
footer {
  background-color: var(--primary-dark);
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-small {
  width: 30px;
  height: 30px;
  margin-bottom: 10px;
}

.footer-links h4,
.footer-contact h4 {
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.footer-links ul {
  list-style: none;
}

.footer-links ul li {
  margin-bottom: 10px;
}

.footer-links ul li a {
  color: rgba(255, 255, 255, 0.8);
}

.footer-links ul li a:hover {
  color: white;
}

.footer-contact p {
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.8);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive styles */
@media (max-width: 768px) {
  .about-content,
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  nav ul {
    flex-direction: column;
    position: absolute;
    top: 70px;
    left: 0;
    width: 100%;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px 0;
    display: none;
  }
  
  nav ul.active {
    display: flex;
  }
  
  nav ul li {
    margin: 10px 0;
    text-align: center;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
}

@media (min-width: 769px) {
  .mobile-menu-toggle {
    display: none;
  }
}