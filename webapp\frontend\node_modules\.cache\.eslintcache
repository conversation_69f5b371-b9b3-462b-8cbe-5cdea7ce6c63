[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "70", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "71", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "74", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "81", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "93", "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "95", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "97", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "100", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "101", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "102", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "103", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "104", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "105"}, {"size": 557, "mtime": 1746952718482, "results": "106", "hashOfConfig": "107"}, {"size": 2728, "mtime": 1748879437279, "results": "108", "hashOfConfig": "107"}, {"size": 996, "mtime": 1746970152489, "results": "109", "hashOfConfig": "107"}, {"size": 10788, "mtime": 1746864244183, "results": "110", "hashOfConfig": "107"}, {"size": 21191, "mtime": 1748751093271, "results": "111", "hashOfConfig": "107"}, {"size": 7269, "mtime": 1748879407880, "results": "112", "hashOfConfig": "107"}, {"size": 2216, "mtime": 1746640055487, "results": "113", "hashOfConfig": "107"}, {"size": 7394, "mtime": 1748034003517, "results": "114", "hashOfConfig": "107"}, {"size": 6749, "mtime": 1746282201800, "results": "115", "hashOfConfig": "107"}, {"size": 18812, "mtime": 1748879486791, "results": "116", "hashOfConfig": "107"}, {"size": 2535, "mtime": 1746647873596, "results": "117", "hashOfConfig": "107"}, {"size": 2050, "mtime": 1746647945415, "results": "118", "hashOfConfig": "107"}, {"size": 700, "mtime": 1747545501078, "results": "119", "hashOfConfig": "107"}, {"size": 17518, "mtime": 1748664526035, "results": "120", "hashOfConfig": "107"}, {"size": 3028, "mtime": 1748816305304, "results": "121", "hashOfConfig": "107"}, {"size": 2070, "mtime": 1748815989656, "results": "122", "hashOfConfig": "107"}, {"size": 1630, "mtime": 1746336079554, "results": "123", "hashOfConfig": "107"}, {"size": 1909, "mtime": 1748722592098, "results": "124", "hashOfConfig": "107"}, {"size": 43832, "mtime": 1748878207301, "results": "125", "hashOfConfig": "107"}, {"size": 324, "mtime": 1748757444974, "results": "126", "hashOfConfig": "107"}, {"size": 9068, "mtime": 1746856425683, "results": "127", "hashOfConfig": "107"}, {"size": 2210, "mtime": 1747432283057, "results": "128", "hashOfConfig": "107"}, {"size": 4494, "mtime": 1748121063631, "results": "129", "hashOfConfig": "107"}, {"size": 38195, "mtime": 1748813903832, "results": "130", "hashOfConfig": "107"}, {"size": 3337, "mtime": 1748816346924, "results": "131", "hashOfConfig": "107"}, {"size": 2958, "mtime": 1748816316425, "results": "132", "hashOfConfig": "107"}, {"size": 3507, "mtime": 1748816326922, "results": "133", "hashOfConfig": "107"}, {"size": 3345, "mtime": 1748816357091, "results": "134", "hashOfConfig": "107"}, {"size": 3340, "mtime": 1748816336281, "results": "135", "hashOfConfig": "107"}, {"size": 2975, "mtime": 1747554796402, "results": "136", "hashOfConfig": "107"}, {"size": 3429, "mtime": 1747721794176, "results": "137", "hashOfConfig": "107"}, {"size": 3109, "mtime": 1747824114392, "results": "138", "hashOfConfig": "107"}, {"size": 2929, "mtime": 1747655572696, "results": "139", "hashOfConfig": "107"}, {"size": 3302, "mtime": 1748000902435, "results": "140", "hashOfConfig": "107"}, {"size": 6125, "mtime": 1748705680231, "results": "141", "hashOfConfig": "107"}, {"size": 5880, "mtime": 1748121404574, "results": "142", "hashOfConfig": "107"}, {"size": 3889, "mtime": 1748664890350, "results": "143", "hashOfConfig": "107"}, {"size": 4720, "mtime": 1746771178920, "results": "144", "hashOfConfig": "107"}, {"size": 7121, "mtime": 1746281148395, "results": "145", "hashOfConfig": "107"}, {"size": 7958, "mtime": 1746280443400, "results": "146", "hashOfConfig": "107"}, {"size": 6259, "mtime": 1746965906057, "results": "147", "hashOfConfig": "107"}, {"size": 4215, "mtime": 1746278746358, "results": "148", "hashOfConfig": "107"}, {"size": 1273, "mtime": 1746809069006, "results": "149", "hashOfConfig": "107"}, {"size": 14270, "mtime": 1748371983481, "results": "150", "hashOfConfig": "107"}, {"size": 2752, "mtime": 1747022186740, "results": "151", "hashOfConfig": "107"}, {"size": 1072, "mtime": 1746637929350, "results": "152", "hashOfConfig": "107"}, {"size": 6745, "mtime": 1747545492454, "results": "153", "hashOfConfig": "107"}, {"size": 41680, "mtime": 1748816669877, "results": "154", "hashOfConfig": "107"}, {"size": 500, "mtime": 1748722841235, "results": "155", "hashOfConfig": "107"}, {"size": 47844, "mtime": 1748876421138, "results": "156", "hashOfConfig": "107"}, {"size": 38669, "mtime": 1748199713253, "results": "157", "hashOfConfig": "107"}, {"size": 1947, "mtime": 1748120984640, "results": "158", "hashOfConfig": "107"}, {"size": 54895, "mtime": 1748370360136, "results": "159", "hashOfConfig": "107"}, {"size": 14635, "mtime": 1748666301849, "results": "160", "hashOfConfig": "107"}, {"size": 5700, "mtime": 1748878577115, "results": "161", "hashOfConfig": "107"}, {"size": 11771, "mtime": 1746948731812, "results": "162", "hashOfConfig": "107"}, {"size": 2211, "mtime": 1748686293878, "results": "163", "hashOfConfig": "107"}, {"size": 9215, "mtime": 1748668814050, "results": "164", "hashOfConfig": "107"}, {"size": 10993, "mtime": 1747154871546, "results": "165", "hashOfConfig": "107"}, {"size": 12150, "mtime": 1748205557322, "results": "166", "hashOfConfig": "107"}, {"size": 24566, "mtime": 1748691444876, "results": "167", "hashOfConfig": "107"}, {"size": 7032, "mtime": 1748069273238, "results": "168", "hashOfConfig": "107"}, {"size": 8589, "mtime": 1748207111023, "results": "169", "hashOfConfig": "107"}, {"size": 9979, "mtime": 1748069243848, "results": "170", "hashOfConfig": "107"}, {"size": 10821, "mtime": 1748069202177, "results": "171", "hashOfConfig": "107"}, {"size": 36555, "mtime": 1747684003188, "results": "172", "hashOfConfig": "107"}, {"size": 9483, "mtime": 1747194869458, "results": "173", "hashOfConfig": "107"}, {"size": 16178, "mtime": 1748875708468, "results": "174", "hashOfConfig": "107"}, {"size": 48588, "mtime": 1747948123233, "results": "175", "hashOfConfig": "107"}, {"size": 92270, "mtime": 1748123070273, "results": "176", "hashOfConfig": "107"}, {"size": 522, "mtime": 1747022186711, "results": "177", "hashOfConfig": "107"}, {"size": 10251, "mtime": 1748805459799, "results": "178", "hashOfConfig": "107"}, {"size": 5994, "mtime": 1748696862956, "results": "179", "hashOfConfig": "107"}, {"size": 1703, "mtime": 1746972529152, "results": "180", "hashOfConfig": "107"}, {"size": 19892, "mtime": 1747554544219, "results": "181", "hashOfConfig": "107"}, {"size": 12050, "mtime": 1747547543421, "results": "182", "hashOfConfig": "107"}, {"size": 1686, "mtime": 1746946499500, "results": "183", "hashOfConfig": "107"}, {"size": 5145, "mtime": 1746914029633, "results": "184", "hashOfConfig": "107"}, {"size": 10721, "mtime": 1748751269815, "results": "185", "hashOfConfig": "107"}, {"size": 22179, "mtime": 1747432554979, "results": "186", "hashOfConfig": "107"}, {"size": 2258, "mtime": 1746946368534, "results": "187", "hashOfConfig": "107"}, {"size": 4094, "mtime": 1748161663641, "results": "188", "hashOfConfig": "107"}, {"size": 5273, "mtime": 1747946737459, "results": "189", "hashOfConfig": "107"}, {"size": 4346, "mtime": 1747491472989, "results": "190", "hashOfConfig": "107"}, {"size": 15571, "mtime": 1747980774491, "results": "191", "hashOfConfig": "107"}, {"size": 6742, "mtime": 1748751174061, "results": "192", "hashOfConfig": "107"}, {"size": 6529, "mtime": 1748664406267, "results": "193", "hashOfConfig": "107"}, {"size": 15764, "mtime": 1748877145346, "results": "194", "hashOfConfig": "107"}, {"size": 6899, "mtime": 1748877131332, "results": "195", "hashOfConfig": "107"}, {"size": 5536, "mtime": 1748670096009, "results": "196", "hashOfConfig": "107"}, {"size": 5457, "mtime": 1748666884369, "results": "197", "hashOfConfig": "107"}, {"size": 5605, "mtime": 1748666925194, "results": "198", "hashOfConfig": "107"}, {"size": 77752, "mtime": 1748878387989, "results": "199", "hashOfConfig": "107"}, {"size": 2807, "mtime": 1748705699971, "results": "200", "hashOfConfig": "107"}, {"size": 22770, "mtime": 1748724489409, "results": "201", "hashOfConfig": "107"}, {"size": 3708, "mtime": 1748705727900, "results": "202", "hashOfConfig": "107"}, {"size": 10270, "mtime": 1748724524628, "results": "203", "hashOfConfig": "107"}, {"size": 8247, "mtime": 1748756088995, "results": "204", "hashOfConfig": "107"}, {"size": 11038, "mtime": 1748756003708, "results": "205", "hashOfConfig": "107"}, {"size": 15055, "mtime": 1748755908778, "results": "206", "hashOfConfig": "107"}, {"size": 16415, "mtime": 1748755956687, "results": "207", "hashOfConfig": "107"}, {"size": 3434, "mtime": 1748755857115, "results": "208", "hashOfConfig": "107"}, {"size": 3483, "mtime": 1748755829302, "results": "209", "hashOfConfig": "107"}, {"size": 3508, "mtime": 1748755842942, "results": "210", "hashOfConfig": "107"}, {"size": 956, "mtime": 1748878396989, "results": "211", "hashOfConfig": "107"}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["527"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", ["528", "529", "530", "531", "532"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["533", "534", "535", "536"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["537", "538", "539", "540", "541", "542", "543", "544", "545", "546"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["547"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["548", "549", "550", "551", "552", "553", "554", "555", "556", "557"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["573"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["574", "575", "576", "577", "578", "579", "580", "581"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["600"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["601"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["602", "603", "604"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["605", "606", "607", "608"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["609", "610"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["611", "612"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["613", "614", "615", "616"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["617", "618", "619", "620"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["621"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["622", "623", "624", "625", "626", "627", "628"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["629", "630", "631"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js", ["632", "633", "634", "635", "636", "637", "638"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["639", "640"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["641", "642"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["643", "644"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["645", "646"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["647", "648", "649", "650", "651", "652", "653", "654"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["669", "670", "671", "672", "673", "674", "675"], ["676"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["700", "701"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["702", "703"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["704", "705"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["717"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["740"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["741"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["742"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["754"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["755", "756", "757", "758"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["799", "800"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["801"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["802", "803"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["824", "825", "826", "827", "828", "829", "830", "831"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["832"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["833", "834", "835"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["836", "837"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["838"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["839"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["840"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["841"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["842", "843", "844", "845", "846", "847"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["848"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js", ["849", "850", "851", "852", "853", "854", "855", "856"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js", ["857", "858", "859", "860", "861", "862", "863"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["864", "865", "866", "867", "868", "869", "870", "871", "872"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["873", "874", "875", "876", "877", "878", "879", "880", "881"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], {"ruleId": "882", "severity": 1, "message": "883", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 14}, {"ruleId": "886", "severity": 1, "message": "887", "line": 97, "column": 71, "nodeType": "888", "messageId": "889", "endLine": 97, "endColumn": 100}, {"ruleId": "886", "severity": 1, "message": "887", "line": 98, "column": 70, "nodeType": "888", "messageId": "889", "endLine": 98, "endColumn": 99}, {"ruleId": "886", "severity": 1, "message": "887", "line": 99, "column": 67, "nodeType": "888", "messageId": "889", "endLine": 99, "endColumn": 96}, {"ruleId": "886", "severity": 1, "message": "887", "line": 100, "column": 76, "nodeType": "888", "messageId": "889", "endLine": 100, "endColumn": 105}, {"ruleId": "886", "severity": 1, "message": "887", "line": 101, "column": 71, "nodeType": "888", "messageId": "889", "endLine": 101, "endColumn": 100}, {"ruleId": "890", "severity": 1, "message": "891", "line": 78, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 78, "endColumn": 115}, {"ruleId": "890", "severity": 1, "message": "891", "line": 80, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 80, "endColumn": 107}, {"ruleId": "890", "severity": 1, "message": "891", "line": 86, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 86, "endColumn": 105}, {"ruleId": "890", "severity": 1, "message": "891", "line": 89, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 89, "endColumn": 41}, {"ruleId": "882", "severity": 1, "message": "894", "line": 13, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 13, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "895", "line": 20, "column": 25, "nodeType": "884", "messageId": "885", "endLine": 20, "endColumn": 34}, {"ruleId": "882", "severity": 1, "message": "896", "line": 21, "column": 19, "nodeType": "884", "messageId": "885", "endLine": 21, "endColumn": 35}, {"ruleId": "882", "severity": 1, "message": "897", "line": 22, "column": 12, "nodeType": "884", "messageId": "885", "endLine": 22, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "898", "line": 23, "column": 18, "nodeType": "884", "messageId": "885", "endLine": 23, "endColumn": 28}, {"ruleId": "882", "severity": 1, "message": "899", "line": 57, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 57, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "900", "line": 58, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 58, "endColumn": 23}, {"ruleId": "882", "severity": 1, "message": "901", "line": 59, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 59, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "902", "line": 60, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 60, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "903", "line": 69, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 69, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "904", "line": 1, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "905", "line": 2, "column": 27, "nodeType": "884", "messageId": "885", "endLine": 2, "endColumn": 31}, {"ruleId": "882", "severity": 1, "message": "906", "line": 2, "column": 33, "nodeType": "884", "messageId": "885", "endLine": 2, "endColumn": 37}, {"ruleId": "882", "severity": 1, "message": "907", "line": 2, "column": 39, "nodeType": "884", "messageId": "885", "endLine": 2, "endColumn": 50}, {"ruleId": "882", "severity": 1, "message": "908", "line": 2, "column": 52, "nodeType": "884", "messageId": "885", "endLine": 2, "endColumn": 66}, {"ruleId": "882", "severity": 1, "message": "894", "line": 2, "column": 68, "nodeType": "884", "messageId": "885", "endLine": 2, "endColumn": 74}, {"ruleId": "882", "severity": 1, "message": "895", "line": 5, "column": 25, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 34}, {"ruleId": "882", "severity": 1, "message": "896", "line": 6, "column": 19, "nodeType": "884", "messageId": "885", "endLine": 6, "endColumn": 35}, {"ruleId": "882", "severity": 1, "message": "897", "line": 7, "column": 12, "nodeType": "884", "messageId": "885", "endLine": 7, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "898", "line": 8, "column": 18, "nodeType": "884", "messageId": "885", "endLine": 8, "endColumn": 28}, {"ruleId": "882", "severity": 1, "message": "909", "line": 43, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 43, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "906", "line": 8, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 8, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "907", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "883", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "905", "line": 11, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 11, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "910", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 10}, {"ruleId": "882", "severity": 1, "message": "911", "line": 15, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 15, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "912", "line": 16, "column": 15, "nodeType": "884", "messageId": "885", "endLine": 16, "endColumn": 27}, {"ruleId": "882", "severity": 1, "message": "913", "line": 17, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 17, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "914", "line": 18, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 18, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "915", "line": 19, "column": 13, "nodeType": "884", "messageId": "885", "endLine": 19, "endColumn": 23}, {"ruleId": "882", "severity": 1, "message": "916", "line": 20, "column": 14, "nodeType": "884", "messageId": "885", "endLine": 20, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "917", "line": 25, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 25, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "918", "line": 28, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 28, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "919", "line": 48, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 48, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "920", "line": 53, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 53, "endColumn": 20}, {"ruleId": "882", "severity": 1, "message": "921", "line": 11, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 11, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "922", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "923", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "924", "line": 7, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 7, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "925", "line": 12, "column": 14, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "911", "line": 13, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 13, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "926", "line": 17, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 17, "endColumn": 23}, {"ruleId": "882", "severity": 1, "message": "918", "line": 21, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 21, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "927", "line": 26, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 26, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "906", "line": 8, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 8, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "907", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "924", "line": 11, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 11, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "928", "line": 14, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 14, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "929", "line": 26, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 26, "endColumn": 16}, {"ruleId": "882", "severity": 1, "message": "930", "line": 30, "column": 15, "nodeType": "884", "messageId": "885", "endLine": 30, "endColumn": 27}, {"ruleId": "882", "severity": 1, "message": "931", "line": 32, "column": 14, "nodeType": "884", "messageId": "885", "endLine": 32, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "932", "line": 33, "column": 15, "nodeType": "884", "messageId": "885", "endLine": 33, "endColumn": 27}, {"ruleId": "882", "severity": 1, "message": "933", "line": 40, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 40, "endColumn": 16}, {"ruleId": "882", "severity": 1, "message": "918", "line": 46, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 46, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "934", "line": 48, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 48, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "927", "line": 50, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 50, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "935", "line": 205, "column": 19, "nodeType": "884", "messageId": "885", "endLine": 205, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "936", "line": 213, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 213, "endColumn": 28}, {"ruleId": "882", "severity": 1, "message": "937", "line": 214, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 214, "endColumn": 23}, {"ruleId": "882", "severity": 1, "message": "938", "line": 214, "column": 25, "nodeType": "884", "messageId": "885", "endLine": 214, "endColumn": 41}, {"ruleId": "939", "severity": 1, "message": "940", "line": 574, "column": 6, "nodeType": "941", "endLine": 574, "endColumn": 15, "suggestions": "942"}, {"ruleId": "882", "severity": 1, "message": "943", "line": 579, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 579, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "944", "line": 1, "column": 27, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 36}, {"ruleId": "882", "severity": 1, "message": "945", "line": 49, "column": 19, "nodeType": "884", "messageId": "885", "endLine": 49, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "910", "line": 15, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 15, "endColumn": 10}, {"ruleId": "882", "severity": 1, "message": "925", "line": 39, "column": 14, "nodeType": "884", "messageId": "885", "endLine": 39, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "946", "line": 43, "column": 16, "nodeType": "884", "messageId": "885", "endLine": 43, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "922", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "923", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "927", "line": 26, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 26, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "947", "line": 48, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 48, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "922", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "947", "line": 37, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 37, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "922", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "947", "line": 52, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 52, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "922", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "923", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "927", "line": 26, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 26, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "947", "line": 48, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 48, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "922", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "923", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "927", "line": 26, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 26, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "947", "line": 48, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 48, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "927", "line": 27, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 27, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "923", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "948", "line": 6, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 6, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "911", "line": 14, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 14, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "918", "line": 23, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 23, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "927", "line": 30, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 30, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "947", "line": 33, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 33, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "949", "line": 38, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 38, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "918", "line": 20, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 20, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "927", "line": 27, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 27, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "949", "line": 35, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 35, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "923", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "948", "line": 6, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 6, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "911", "line": 14, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 14, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "918", "line": 23, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 23, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "927", "line": 30, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 30, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "947", "line": 33, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 33, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "949", "line": 38, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 38, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "918", "line": 24, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 24, "endColumn": 26}, {"ruleId": "939", "severity": 1, "message": "950", "line": 53, "column": 6, "nodeType": "941", "endLine": 53, "endColumn": 18, "suggestions": "951"}, {"ruleId": "882", "severity": 1, "message": "952", "line": 1, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "953", "line": 5, "column": 7, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "910", "line": 14, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 14, "endColumn": 10}, {"ruleId": "882", "severity": 1, "message": "954", "line": 28, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 28, "endColumn": 18}, {"ruleId": "882", "severity": 1, "message": "952", "line": 1, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "953", "line": 5, "column": 7, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "906", "line": 8, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 8, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "907", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "883", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "955", "line": 23, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 23, "endColumn": 15}, {"ruleId": "882", "severity": 1, "message": "956", "line": 24, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 24, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "912", "line": 46, "column": 15, "nodeType": "884", "messageId": "885", "endLine": 46, "endColumn": 27}, {"ruleId": "882", "severity": 1, "message": "957", "line": 47, "column": 12, "nodeType": "884", "messageId": "885", "endLine": 47, "endColumn": 21}, {"ruleId": "939", "severity": 1, "message": "958", "line": 134, "column": 6, "nodeType": "941", "endLine": 134, "endColumn": 18, "suggestions": "959"}, {"ruleId": "882", "severity": 1, "message": "906", "line": 8, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 8, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "907", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "883", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "955", "line": 23, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 23, "endColumn": 15}, {"ruleId": "882", "severity": 1, "message": "956", "line": 24, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 24, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "910", "line": 25, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 25, "endColumn": 10}, {"ruleId": "882", "severity": 1, "message": "924", "line": 29, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 29, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "914", "line": 39, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 39, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "912", "line": 43, "column": 15, "nodeType": "884", "messageId": "885", "endLine": 43, "endColumn": 27}, {"ruleId": "882", "severity": 1, "message": "960", "line": 44, "column": 14, "nodeType": "884", "messageId": "885", "endLine": 44, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "961", "line": 50, "column": 69, "nodeType": "884", "messageId": "885", "endLine": 50, "endColumn": 76}, {"ruleId": "882", "severity": 1, "message": "962", "line": 79, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 79, "endColumn": 26}, {"ruleId": "939", "severity": 1, "message": "963", "line": 179, "column": 6, "nodeType": "941", "endLine": 179, "endColumn": 8, "suggestions": "964"}, {"ruleId": "882", "severity": 1, "message": "965", "line": 697, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 697, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "966", "line": 20, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 20, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "967", "line": 21, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 21, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "968", "line": 22, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 22, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "905", "line": 23, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 23, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "969", "line": 26, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 26, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "970", "line": 69, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 69, "endColumn": 22}, {"ruleId": "971", "severity": 1, "message": "972", "line": 466, "column": 9, "nodeType": "973", "messageId": "974", "endLine": 469, "endColumn": 10}, {"ruleId": "939", "severity": 1, "message": "975", "line": 95, "column": 6, "nodeType": "941", "endLine": 95, "endColumn": 21, "suggestions": "976", "suppressions": "977"}, {"ruleId": "890", "severity": 1, "message": "891", "line": 260, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 264, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "891", "line": 274, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 274, "endColumn": 70}, {"ruleId": "890", "severity": 1, "message": "891", "line": 278, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 278, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "891", "line": 333, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 338, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "891", "line": 435, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 439, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "891", "line": 451, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 451, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "891", "line": 668, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 668, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 677, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 677, "endColumn": 70}, {"ruleId": "890", "severity": 1, "message": "891", "line": 681, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 681, "endColumn": 54}, {"ruleId": "882", "severity": 1, "message": "978", "line": 755, "column": 17, "nodeType": "884", "messageId": "885", "endLine": 755, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "891", "line": 775, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 779, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "891", "line": 794, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 798, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "891", "line": 801, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 804, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "891", "line": 810, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 814, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "891", "line": 817, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 820, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "891", "line": 885, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 889, "endColumn": 11}, {"ruleId": "979", "severity": 1, "message": "980", "line": 955, "column": 3, "nodeType": "981", "messageId": "982", "endLine": 955, "endColumn": 29}, {"ruleId": "979", "severity": 1, "message": "983", "line": 1143, "column": 3, "nodeType": "981", "messageId": "982", "endLine": 1143, "endColumn": 23}, {"ruleId": "979", "severity": 1, "message": "984", "line": 1238, "column": 3, "nodeType": "981", "messageId": "982", "endLine": 1238, "endColumn": 20}, {"ruleId": "890", "severity": 1, "message": "891", "line": 1287, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 1287, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 1317, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 1317, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 1370, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 1370, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 1412, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 1412, "endColumn": 163}, {"ruleId": "882", "severity": 1, "message": "985", "line": 6, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 6, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "910", "line": 11, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 11, "endColumn": 10}, {"ruleId": "882", "severity": 1, "message": "922", "line": 2, "column": 15, "nodeType": "884", "messageId": "885", "endLine": 2, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "986", "line": 16, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 16, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "952", "line": 1, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "953", "line": 5, "column": 7, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "987", "line": 3, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 3, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "988", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 6}, {"ruleId": "882", "severity": 1, "message": "989", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "990", "line": 6, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 6, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "991", "line": 7, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 7, "endColumn": 6}, {"ruleId": "882", "severity": 1, "message": "992", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "993", "line": 36, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 36, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "994", "line": 50, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 50, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "995", "line": 64, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 64, "endColumn": 20}, {"ruleId": "882", "severity": 1, "message": "996", "line": 88, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 88, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "997", "line": 104, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 104, "endColumn": 30}, {"ruleId": "882", "severity": 1, "message": "998", "line": 3, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 3, "endColumn": 12}, {"ruleId": "882", "severity": 1, "message": "990", "line": 3, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 3, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "991", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 6}, {"ruleId": "882", "severity": 1, "message": "999", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "1000", "line": 6, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 6, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "1001", "line": 7, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 7, "endColumn": 16}, {"ruleId": "882", "severity": 1, "message": "1002", "line": 8, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 8, "endColumn": 10}, {"ruleId": "882", "severity": 1, "message": "992", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "1003", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "987", "line": 11, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 11, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "988", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 6}, {"ruleId": "882", "severity": 1, "message": "989", "line": 13, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 13, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "1004", "line": 14, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 14, "endColumn": 16}, {"ruleId": "882", "severity": 1, "message": "1005", "line": 15, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 15, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "998", "line": 16, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 16, "endColumn": 12}, {"ruleId": "882", "severity": 1, "message": "1006", "line": 18, "column": 40, "nodeType": "884", "messageId": "885", "endLine": 18, "endColumn": 44}, {"ruleId": "882", "severity": 1, "message": "1007", "line": 47, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 47, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "1008", "line": 64, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 64, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "1009", "line": 71, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 71, "endColumn": 20}, {"ruleId": "882", "severity": 1, "message": "996", "line": 79, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 79, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "997", "line": 95, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 95, "endColumn": 30}, {"ruleId": "882", "severity": 1, "message": "1010", "line": 299, "column": 27, "nodeType": "884", "messageId": "885", "endLine": 299, "endColumn": 37}, {"ruleId": "882", "severity": 1, "message": "1011", "line": 300, "column": 27, "nodeType": "884", "messageId": "885", "endLine": 300, "endColumn": 36}, {"ruleId": "882", "severity": 1, "message": "923", "line": 3, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 3, "endColumn": 8}, {"ruleId": "939", "severity": 1, "message": "1012", "line": 54, "column": 6, "nodeType": "941", "endLine": 54, "endColumn": 34, "suggestions": "1013"}, {"ruleId": "882", "severity": 1, "message": "1014", "line": 25, "column": 13, "nodeType": "884", "messageId": "885", "endLine": 25, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "1015", "line": 33, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 33, "endColumn": 15}, {"ruleId": "882", "severity": 1, "message": "1016", "line": 34, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 34, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "1017", "line": 35, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 35, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "1018", "line": 36, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 36, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "1019", "line": 37, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 37, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1020", "line": 41, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 41, "endColumn": 20}, {"ruleId": "882", "severity": 1, "message": "1021", "line": 43, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 43, "endColumn": 34}, {"ruleId": "882", "severity": 1, "message": "1022", "line": 69, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 69, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1023", "line": 69, "column": 19, "nodeType": "884", "messageId": "885", "endLine": 69, "endColumn": 29}, {"ruleId": "939", "severity": 1, "message": "1024", "line": 88, "column": 6, "nodeType": "941", "endLine": 88, "endColumn": 18, "suggestions": "1025"}, {"ruleId": "939", "severity": 1, "message": "1026", "line": 448, "column": 6, "nodeType": "941", "endLine": 448, "endColumn": 28, "suggestions": "1027"}, {"ruleId": "882", "severity": 1, "message": "1028", "line": 4, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 4, "endColumn": 12}, {"ruleId": "882", "severity": 1, "message": "1029", "line": 8, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 8, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "1030", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "1031", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 15}, {"ruleId": "939", "severity": 1, "message": "1012", "line": 46, "column": 6, "nodeType": "941", "endLine": 46, "endColumn": 18, "suggestions": "1032"}, {"ruleId": "882", "severity": 1, "message": "1033", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "966", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "967", "line": 11, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 11, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "968", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "1031", "line": 33, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 33, "endColumn": 15}, {"ruleId": "882", "severity": 1, "message": "1034", "line": 35, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 35, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "960", "line": 42, "column": 14, "nodeType": "884", "messageId": "885", "endLine": 42, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "1015", "line": 52, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 52, "endColumn": 15}, {"ruleId": "882", "severity": 1, "message": "1016", "line": 53, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 53, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "1017", "line": 54, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 54, "endColumn": 22}, {"ruleId": "882", "severity": 1, "message": "1018", "line": 55, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 55, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "1019", "line": 56, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 56, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1035", "line": 57, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 57, "endColumn": 15}, {"ruleId": "882", "severity": 1, "message": "1036", "line": 58, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 58, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "1037", "line": 59, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 59, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "934", "line": 72, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 72, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1038", "line": 79, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 79, "endColumn": 23}, {"ruleId": "882", "severity": 1, "message": "1039", "line": 79, "column": 25, "nodeType": "884", "messageId": "885", "endLine": 79, "endColumn": 41}, {"ruleId": "882", "severity": 1, "message": "1040", "line": 80, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 80, "endColumn": 27}, {"ruleId": "882", "severity": 1, "message": "1041", "line": 85, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 85, "endColumn": 26}, {"ruleId": "939", "severity": 1, "message": "1012", "line": 105, "column": 6, "nodeType": "941", "endLine": 105, "endColumn": 18, "suggestions": "1042"}, {"ruleId": "939", "severity": 1, "message": "1043", "line": 112, "column": 6, "nodeType": "941", "endLine": 112, "endColumn": 20, "suggestions": "1044"}, {"ruleId": "939", "severity": 1, "message": "1045", "line": 127, "column": 6, "nodeType": "941", "endLine": 127, "endColumn": 34, "suggestions": "1046"}, {"ruleId": "882", "severity": 1, "message": "1047", "line": 283, "column": 13, "nodeType": "884", "messageId": "885", "endLine": 283, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "969", "line": 17, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 17, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1031", "line": 34, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 34, "endColumn": 15}, {"ruleId": "882", "severity": 1, "message": "1034", "line": 35, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 35, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "1048", "line": 39, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 39, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "1015", "line": 51, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 51, "endColumn": 15}, {"ruleId": "882", "severity": 1, "message": "1016", "line": 52, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 52, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "1018", "line": 54, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 54, "endColumn": 21}, {"ruleId": "882", "severity": 1, "message": "1019", "line": 55, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 55, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1021", "line": 62, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 62, "endColumn": 34}, {"ruleId": "882", "severity": 1, "message": "1049", "line": 105, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 105, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "1050", "line": 105, "column": 28, "nodeType": "884", "messageId": "885", "endLine": 105, "endColumn": 47}, {"ruleId": "939", "severity": 1, "message": "1043", "line": 145, "column": 6, "nodeType": "941", "endLine": 145, "endColumn": 18, "suggestions": "1051"}, {"ruleId": "882", "severity": 1, "message": "1052", "line": 701, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 701, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "1053", "line": 1311, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 1311, "endColumn": 28}, {"ruleId": "882", "severity": 1, "message": "1054", "line": 1316, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 1316, "endColumn": 30}, {"ruleId": "882", "severity": 1, "message": "1055", "line": 1883, "column": 9, "nodeType": "884", "messageId": "885", "endLine": 1883, "endColumn": 23}, {"ruleId": "882", "severity": 1, "message": "952", "line": 1, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "953", "line": 5, "column": 7, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "1056", "line": 1, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "952", "line": 1, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "953", "line": 5, "column": 7, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "952", "line": 1, "column": 8, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "953", "line": 5, "column": 7, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "1057", "line": 83, "column": 13, "nodeType": "884", "messageId": "885", "endLine": 83, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "891", "line": 109, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 109, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 123, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 123, "endColumn": 70}, {"ruleId": "890", "severity": 1, "message": "891", "line": 127, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 127, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "891", "line": 212, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 212, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 226, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 226, "endColumn": 70}, {"ruleId": "890", "severity": 1, "message": "891", "line": 230, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 230, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "891", "line": 271, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 271, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 280, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 280, "endColumn": 70}, {"ruleId": "890", "severity": 1, "message": "891", "line": 284, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 284, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "891", "line": 320, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 320, "endColumn": 70}, {"ruleId": "890", "severity": 1, "message": "891", "line": 324, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 324, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "891", "line": 360, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 360, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 369, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 369, "endColumn": 70}, {"ruleId": "890", "severity": 1, "message": "891", "line": 373, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 373, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "891", "line": 450, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 450, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "891", "line": 459, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 459, "endColumn": 70}, {"ruleId": "890", "severity": 1, "message": "891", "line": 463, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 463, "endColumn": 54}, {"ruleId": "882", "severity": 1, "message": "1058", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "913", "line": 27, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 27, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "929", "line": 30, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 30, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "1017", "line": 34, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 34, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "1022", "line": 49, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 49, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1023", "line": 49, "column": 19, "nodeType": "884", "messageId": "885", "endLine": 49, "endColumn": 29}, {"ruleId": "939", "severity": 1, "message": "1012", "line": 64, "column": 6, "nodeType": "941", "endLine": 64, "endColumn": 32, "suggestions": "1059"}, {"ruleId": "882", "severity": 1, "message": "1060", "line": 270, "column": 17, "nodeType": "884", "messageId": "885", "endLine": 270, "endColumn": 23}, {"ruleId": "882", "severity": 1, "message": "1061", "line": 17, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 17, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "968", "line": 16, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 16, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "967", "line": 17, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 17, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "966", "line": 19, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 19, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "929", "line": 14, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 14, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "1062", "line": 43, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 43, "endColumn": 26}, {"ruleId": "882", "severity": 1, "message": "924", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "1063", "line": 33, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 33, "endColumn": 29}, {"ruleId": "882", "severity": 1, "message": "1064", "line": 3, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 3, "endColumn": 6}, {"ruleId": "882", "severity": 1, "message": "910", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 10}, {"ruleId": "882", "severity": 1, "message": "1065", "line": 20, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 20, "endColumn": 19}, {"ruleId": "939", "severity": 1, "message": "1066", "line": 136, "column": 6, "nodeType": "941", "endLine": 136, "endColumn": 18, "suggestions": "1067"}, {"ruleId": "939", "severity": 1, "message": "1068", "line": 141, "column": 6, "nodeType": "941", "endLine": 141, "endColumn": 52, "suggestions": "1069"}, {"ruleId": "939", "severity": 1, "message": "1070", "line": 146, "column": 6, "nodeType": "941", "endLine": 146, "endColumn": 62, "suggestions": "1071"}, {"ruleId": "939", "severity": 1, "message": "1072", "line": 151, "column": 6, "nodeType": "941", "endLine": 151, "endColumn": 28, "suggestions": "1073"}, {"ruleId": "939", "severity": 1, "message": "1074", "line": 160, "column": 6, "nodeType": "941", "endLine": 160, "endColumn": 39, "suggestions": "1075"}, {"ruleId": "939", "severity": 1, "message": "1076", "line": 66, "column": 6, "nodeType": "941", "endLine": 66, "endColumn": 18, "suggestions": "1077"}, {"ruleId": "882", "severity": 1, "message": "906", "line": 8, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 8, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "907", "line": 9, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 9, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "948", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 9}, {"ruleId": "882", "severity": 1, "message": "1030", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 11}, {"ruleId": "882", "severity": 1, "message": "1078", "line": 21, "column": 17, "nodeType": "884", "messageId": "885", "endLine": 21, "endColumn": 31}, {"ruleId": "882", "severity": 1, "message": "898", "line": 24, "column": 17, "nodeType": "884", "messageId": "885", "endLine": 24, "endColumn": 27}, {"ruleId": "939", "severity": 1, "message": "1079", "line": 180, "column": 6, "nodeType": "941", "endLine": 180, "endColumn": 25, "suggestions": "1080"}, {"ruleId": "886", "severity": 1, "message": "1081", "line": 243, "column": 15, "nodeType": "888", "messageId": "889", "endLine": 248, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "923", "line": 5, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 5, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "956", "line": 15, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 15, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "910", "line": 16, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 16, "endColumn": 10}, {"ruleId": "882", "severity": 1, "message": "914", "line": 28, "column": 11, "nodeType": "884", "messageId": "885", "endLine": 28, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "915", "line": 29, "column": 13, "nodeType": "884", "messageId": "885", "endLine": 29, "endColumn": 23}, {"ruleId": "882", "severity": 1, "message": "1082", "line": 39, "column": 34, "nodeType": "884", "messageId": "885", "endLine": 39, "endColumn": 59}, {"ruleId": "882", "severity": 1, "message": "1022", "line": 41, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 41, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "944", "line": 1, "column": 27, "nodeType": "884", "messageId": "885", "endLine": 1, "endColumn": 36}, {"ruleId": "882", "severity": 1, "message": "906", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "907", "line": 11, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 11, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "922", "line": 12, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 12, "endColumn": 13}, {"ruleId": "882", "severity": 1, "message": "1061", "line": 27, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 27, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "913", "line": 30, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 30, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1083", "line": 33, "column": 17, "nodeType": "884", "messageId": "885", "endLine": 33, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "898", "line": 34, "column": 17, "nodeType": "884", "messageId": "885", "endLine": 34, "endColumn": 27}, {"ruleId": "882", "severity": 1, "message": "925", "line": 35, "column": 14, "nodeType": "884", "messageId": "885", "endLine": 35, "endColumn": 25}, {"ruleId": "882", "severity": 1, "message": "906", "line": 10, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 10, "endColumn": 7}, {"ruleId": "882", "severity": 1, "message": "907", "line": 11, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 11, "endColumn": 14}, {"ruleId": "882", "severity": 1, "message": "1061", "line": 27, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 27, "endColumn": 8}, {"ruleId": "882", "severity": 1, "message": "1084", "line": 28, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 28, "endColumn": 12}, {"ruleId": "882", "severity": 1, "message": "1085", "line": 29, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 29, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "1086", "line": 30, "column": 3, "nodeType": "884", "messageId": "885", "endLine": 30, "endColumn": 19}, {"ruleId": "882", "severity": 1, "message": "913", "line": 34, "column": 10, "nodeType": "884", "messageId": "885", "endLine": 34, "endColumn": 17}, {"ruleId": "882", "severity": 1, "message": "1087", "line": 37, "column": 17, "nodeType": "884", "messageId": "885", "endLine": 37, "endColumn": 31}, {"ruleId": "939", "severity": 1, "message": "1088", "line": 98, "column": 6, "nodeType": "941", "endLine": 98, "endColumn": 24, "suggestions": "1089"}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "react/jsx-pascal-case", "Imported JSX component CertificazioneCEI64_8Page must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1090"], "'handleOpenDetails' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'InventoryIcon' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'Button' is defined but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1091"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["1092"], "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1093"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1094"], ["1095"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'filteredCavi' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1096"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1097"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1098"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", ["1099"], "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["1100"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1101"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1102"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["1103"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'config' is defined but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["1104"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "'CircularProgress' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1105"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1106"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1107"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1108"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1109"], "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1110"], "'AssignmentIcon' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'getInitialAction'. Either include it or remove the dependency array.", ["1111"], "Imported JSX component CertificazioneCEI64_8 must be in PascalCase or SCREAMING_SNAKE_CASE", "'setSelectedCertificazione' is assigned a value but never used.", "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1112"], {"desc": "1113", "fix": "1114"}, {"desc": "1115", "fix": "1116"}, {"desc": "1117", "fix": "1118"}, {"desc": "1119", "fix": "1120"}, {"desc": "1121", "fix": "1122"}, {"kind": "1123", "justification": "1124"}, {"desc": "1125", "fix": "1126"}, {"desc": "1127", "fix": "1128"}, {"desc": "1129", "fix": "1130"}, {"desc": "1131", "fix": "1132"}, {"desc": "1131", "fix": "1133"}, {"desc": "1134", "fix": "1135"}, {"desc": "1136", "fix": "1137"}, {"desc": "1138", "fix": "1139"}, {"desc": "1140", "fix": "1141"}, {"desc": "1142", "fix": "1143"}, {"desc": "1144", "fix": "1145"}, {"desc": "1146", "fix": "1147"}, {"desc": "1148", "fix": "1149"}, {"desc": "1150", "fix": "1151"}, {"desc": "1152", "fix": "1153"}, {"desc": "1154", "fix": "1155"}, {"desc": "1156", "fix": "1157"}, "Update the dependencies array to be: [caviAttivi, caviSpare, error, filters, user]", {"range": "1158", "text": "1159"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1160", "text": "1161"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "1162", "text": "1163"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1164", "text": "1165"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "1166", "text": "1167"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1168", "text": "1169"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1170", "text": "1171"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1172", "text": "1173"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1174", "text": "1175"}, {"range": "1176", "text": "1175"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1177", "text": "1178"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1179", "text": "1180"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1181", "text": "1182"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1183", "text": "1184"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1185", "text": "1186"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1187", "text": "1188"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1189", "text": "1190"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1191", "text": "1192"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1193", "text": "1194"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1195", "text": "1196"}, "Update the dependencies array to be: [getInitialAction, location.pathname]", {"range": "1197", "text": "1198"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1199", "text": "1200"}, [23147, 23156], "[caviAttivi, caviSpare, error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [6583, 6585], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1014, 1026], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]", [3608, 3620], "[cantiereId, loadInitialData]", [3705, 3751], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [3835, 3891], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [3997, 4019], "[calculateStatistics, cavi, certificazioni]", [4241, 4274], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1528, 1540], "[cantiereId, loadComande, loadStatistiche]", [4982, 5001], "[getInitialAction, location.pathname]", [2516, 2534], "[certificazioneId, loadProve]"]